# -*- coding: utf-8 -*-
from pytdx.hq import TdxHq_API

api = TdxHq_API()

# Connect to market data server
if api.connect('119.147.212.81', 7709):
    print("Connection successful!")

    # Get stock quotes
    result = api.get_security_quotes([(0, '000001'), (1, '600300')])
    print("Stock market data:")
    print(result)

    # Disconnect
    api.disconnect()
    print("Connection closed")
else:
    print("Connection failed!")
