from datetime import datetime
import backtrader as bt
import pandas as pd
import numpy as np

# Create a subclass of Strategy to define the indicators and logic
class SmaCross(bt.Strategy):
    # list of parameters which are configurable for the strategy
    params = dict(
        pfast=10,  # period for the fast moving average
        pslow=30   # period for the slow moving average
    )

    def __init__(self):
        sma1 = bt.ind.SMA(period=self.p.pfast)  # fast moving average
        sma2 = bt.ind.SMA(period=self.p.pslow)  # slow moving average
        self.crossover = bt.ind.CrossOver(sma1, sma2)  # crossover signal

    def next(self):
        if not self.position:  # not in the market
            if self.crossover > 0:  # if fast crosses slow to the upside
                self.buy()  # enter long
                print(f'BUY CREATE, {self.data.datetime.date(0)}, Price: {self.data.close[0]:.2f}')

        elif self.crossover < 0:  # in the market & cross to the downside
            self.close()  # close long position
            print(f'SELL CREATE, {self.data.datetime.date(0)}, Price: {self.data.close[0]:.2f}')

# Create sample data instead of downloading from Yahoo
def create_sample_data():
    # Generate sample stock data
    dates = pd.date_range('2011-01-01', '2012-12-31', freq='D')
    np.random.seed(42)  # for reproducible results

    # Generate realistic stock price data
    price = 100
    prices = []
    for i in range(len(dates)):
        price += np.random.normal(0, 1)  # random walk
        price = max(price, 10)  # prevent negative prices
        prices.append(price)

    # Create OHLC data
    data = []
    for i, (date, close) in enumerate(zip(dates, prices)):
        high = close + abs(np.random.normal(0, 0.5))
        low = close - abs(np.random.normal(0, 0.5))
        open_price = close + np.random.normal(0, 0.3)
        volume = np.random.randint(1000000, 5000000)

        data.append([date, open_price, high, low, close, volume])

    df = pd.DataFrame(data, columns=['datetime', 'open', 'high', 'low', 'close', 'volume'])
    df.set_index('datetime', inplace=True)
    return df

# Create cerebro engine
cerebro = bt.Cerebro()

# Set initial cash
cerebro.broker.setcash(100000.0)

# Create sample data
sample_data = create_sample_data()

# Create data feed from pandas DataFrame
data = bt.feeds.PandasData(dataname=sample_data)

cerebro.adddata(data)  # Add the data feed
cerebro.addstrategy(SmaCross)  # Add the trading strategy

print('Starting Portfolio Value: %.2f' % cerebro.broker.getvalue())
cerebro.run()  # run it all
print('Final Portfolio Value: %.2f' % cerebro.broker.getvalue())

# Plot the results (this might require matplotlib)
try:
    cerebro.plot()
except Exception as e:
    print(f"Plotting failed: {e}")
    print("You might need to install matplotlib: pip install matplotlib")