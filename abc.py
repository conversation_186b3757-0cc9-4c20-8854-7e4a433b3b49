# -*- coding: utf-8 -*-
import tushare as ts
import pandas as pd
import sys
import io

# 设置输出编码为UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 设置Tushare token
ts.set_token('abfde44f5c8aaf5cc632a9c76060fb6c78821d7679cb61ceb79f2345')
pro = ts.pro_api()

print("正在获取股票数据...")

try:
    # 获取平安银行(000001.SZ)的日线数据
    df = pro.daily(ts_code='000001.SZ', start_date='20180701', end_date='20180718')

    if df is not None and not df.empty:
        print("成功: 数据获取成功!")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        print("\n数据列名:")
        print(df.columns.tolist())

        print("\n前5行数据:")
        print(df.head())

        print("\n数据基本统计:")
        print(df.describe())

        print("\n价格范围:")
        print(f"最高价: {df['high'].max():.2f}")
        print(f"最低价: {df['low'].min():.2f}")
        print(f"平均收盘价: {df['close'].mean():.2f}")

        # 保存数据到CSV文件
        df.to_csv('stock_data.csv', index=False)
        print("\n数据已保存到 stock_data.csv")

    else:
        print("错误: 获取到的数据为空")

except Exception as e:
    print(f"错误: 数据获取失败: {e}")
    print("可能的原因:")
    print("1. 网络连接问题")
    print("2. Tushare token无效或过期")
    print("3. API调用次数超限")
    print("4. 股票代码或日期格式错误")