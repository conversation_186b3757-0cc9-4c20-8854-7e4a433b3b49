# -*- coding: utf-8 -*-
import tushare as ts
import pandas as pd

# 设置Tushare token
ts.set_token('abfde44f5c8aaf5cc632a9c76060fb6c78821d7679cb61ceb79f2345')
pro = ts.pro_api()

print("Fetching stock data...")

try:
    # Get daily data for Ping An Bank (000001.SZ)
    df = pro.daily(ts_code='000001.SZ', start_date='********', end_date='********')

    if df is not None and not df.empty:
        print("SUCCESS: Data fetched successfully!")
        print(f"Data rows: {len(df)}")
        print(f"Data columns: {len(df.columns)}")
        print("\nColumn names:")
        print(df.columns.tolist())

        print("\nFirst 5 rows:")
        print(df.head())

        print("\nBasic statistics:")
        print(df.describe())

        print("\nPrice range:")
        print(f"Highest price: {df['high'].max():.2f}")
        print(f"Lowest price: {df['low'].min():.2f}")
        print(f"Average close price: {df['close'].mean():.2f}")

        # Save data to CSV file
        df.to_csv('stock_data.csv', index=False)
        print("\nData saved to stock_data.csv")

    else:
        print("ERROR: No data received")

except Exception as e:
    print(f"ERROR: Data fetch failed: {e}")
    print("Possible reasons:")
    print("1. Network connection issue")
    print("2. Invalid or expired Tushare token")
    print("3. API call limit exceeded")
    print("4. Wrong stock code or date format")